import { MigrationInterface, QueryRunner } from "typeorm";

export class Init1753638756040 implements MigrationInterface {
    name = 'Init1753638756040'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "kpi_deliverable_type" ("code" nvarchar(255) NOT NULL, CONSTRAINT "PK_c542aa8118bfca09c13c1470f4b" PRIMARY KEY ("code"))`);
        await queryRunner.query(`CREATE TABLE "kpi_deliverable_usage" ("uid" uniqueidentifier NOT NULL CONSTRAINT "DF_f9342434147f925830e653ed8ad" DEFAULT NEWID(), "deleted_at" datetime2, "created_at" datetime2 NOT NULL CONSTRAINT "DF_6eb8762a310c7fb5c135a5113a1" DEFAULT getdate(), "updated_at" datetime2 NOT NULL CONSTRAINT "DF_75852a2d747a6798c659c21b8e0" DEFAULT getdate(), "deleted_by" uniqueidentifier, "created_by" uniqueidentifier NOT NULL, "updated_by" uniqueidentifier NOT NULL, "origin" nvarchar(255) NOT NULL, "used_by" nvarchar(255) NOT NULL, "deliverable_code" uniqueidentifier, CONSTRAINT "PK_f9342434147f925830e653ed8ad" PRIMARY KEY ("uid"))`);
        await queryRunner.query(`CREATE TABLE "kpi_deliverable" ("uid" uniqueidentifier NOT NULL CONSTRAINT "DF_46d3558d1d10252fac7f0b9ef52" DEFAULT NEWID(), "deleted_at" datetime2, "created_at" datetime2 NOT NULL CONSTRAINT "DF_53c647fa7130d745c140b76a9da" DEFAULT getdate(), "updated_at" datetime2 NOT NULL CONSTRAINT "DF_84cfde460b8b3fe6f204ce2b9e7" DEFAULT getdate(), "deleted_by" uniqueidentifier, "created_by" uniqueidentifier NOT NULL, "updated_by" uniqueidentifier NOT NULL, "origin" nvarchar(255) NOT NULL, "name" nvarchar(255) NOT NULL, "function" nvarchar(255) NOT NULL, "frequency" nvarchar(255) NOT NULL, "is_active" bit NOT NULL, "source" nvarchar(255) NOT NULL, "usage" int NOT NULL, "calculation_method" nvarchar(MAX) NOT NULL, "definition" nvarchar(MAX) NOT NULL, "pa_value" nvarchar(MAX) NOT NULL, "bu_level_aggregation" nvarchar(MAX), "is_cascading" bit NOT NULL, "deliverable_type_code" nvarchar(255), "origin_project_code" uniqueidentifier, CONSTRAINT "PK_46d3558d1d10252fac7f0b9ef52" PRIMARY KEY ("uid"))`);
        await queryRunner.query(`CREATE TABLE "kpi_project" ("uid" uniqueidentifier NOT NULL CONSTRAINT "DF_9f79970f1293733965bba5107c8" DEFAULT NEWID(), "deleted_at" datetime2, "created_at" datetime2 NOT NULL CONSTRAINT "DF_c81346043b7216cd399367ca4c3" DEFAULT getdate(), "updated_at" datetime2 NOT NULL CONSTRAINT "DF_055d261b3ab293cc8724e526d3b" DEFAULT getdate(), "deleted_by" uniqueidentifier, "created_by" uniqueidentifier NOT NULL, "updated_by" uniqueidentifier NOT NULL, "origin" nvarchar(255) NOT NULL, "name" nvarchar(255) NOT NULL, "function" nvarchar(255) NOT NULL, "frequency" nvarchar(255) NOT NULL, "is_active" bit NOT NULL, "source" nvarchar(255) NOT NULL, "usage" int NOT NULL, "objective" nvarchar(255), "date_start" datetime2 NOT NULL, "date_end" datetime2 NOT NULL, "project_type" nvarchar(255) NOT NULL, CONSTRAINT "PK_9f79970f1293733965bba5107c8" PRIMARY KEY ("uid"))`);
        await queryRunner.query(`CREATE TABLE "kpi_project_deliverables" ("code_project" uniqueidentifier NOT NULL, "code_deliverable" uniqueidentifier NOT NULL, CONSTRAINT "PK_597a6d165da21c64461bd45dc87" PRIMARY KEY ("code_project", "code_deliverable"))`);
        await queryRunner.query(`CREATE INDEX "IDX_f9a5d7605777fdf1f4d6e0fb09" ON "kpi_project_deliverables" ("code_project") `);
        await queryRunner.query(`CREATE INDEX "IDX_5c99fac8df61b9d80dfd8c4a00" ON "kpi_project_deliverables" ("code_deliverable") `);
        await queryRunner.query(`ALTER TABLE "kpi_deliverable_usage" ADD CONSTRAINT "FK_2d9f8eef31bc0eea931a3f458ba" FOREIGN KEY ("deliverable_code") REFERENCES "kpi_deliverable"("uid") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "kpi_deliverable" ADD CONSTRAINT "FK_0ff18e431dbd8344bb8e94704cb" FOREIGN KEY ("deliverable_type_code") REFERENCES "kpi_deliverable_type"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "kpi_deliverable" ADD CONSTRAINT "FK_0c4cb08e5a2445500f45996a75b" FOREIGN KEY ("origin_project_code") REFERENCES "kpi_project"("uid") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "kpi_project_deliverables" ADD CONSTRAINT "FK_f9a5d7605777fdf1f4d6e0fb094" FOREIGN KEY ("code_project") REFERENCES "kpi_project"("uid") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "kpi_project_deliverables" ADD CONSTRAINT "FK_5c99fac8df61b9d80dfd8c4a009" FOREIGN KEY ("code_deliverable") REFERENCES "kpi_deliverable"("uid") ON DELETE CASCADE ON UPDATE CASCADE`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "kpi_project_deliverables" DROP CONSTRAINT "FK_5c99fac8df61b9d80dfd8c4a009"`);
        await queryRunner.query(`ALTER TABLE "kpi_project_deliverables" DROP CONSTRAINT "FK_f9a5d7605777fdf1f4d6e0fb094"`);
        await queryRunner.query(`ALTER TABLE "kpi_deliverable" DROP CONSTRAINT "FK_0c4cb08e5a2445500f45996a75b"`);
        await queryRunner.query(`ALTER TABLE "kpi_deliverable" DROP CONSTRAINT "FK_0ff18e431dbd8344bb8e94704cb"`);
        await queryRunner.query(`ALTER TABLE "kpi_deliverable_usage" DROP CONSTRAINT "FK_2d9f8eef31bc0eea931a3f458ba"`);
        await queryRunner.query(`DROP INDEX "IDX_5c99fac8df61b9d80dfd8c4a00" ON "kpi_project_deliverables"`);
        await queryRunner.query(`DROP INDEX "IDX_f9a5d7605777fdf1f4d6e0fb09" ON "kpi_project_deliverables"`);
        await queryRunner.query(`DROP TABLE "kpi_project_deliverables"`);
        await queryRunner.query(`DROP TABLE "kpi_project"`);
        await queryRunner.query(`DROP TABLE "kpi_deliverable"`);
        await queryRunner.query(`DROP TABLE "kpi_deliverable_usage"`);
        await queryRunner.query(`DROP TABLE "kpi_deliverable_type"`);
    }

}
