import { MigrationInterface, QueryRunner } from "typeorm";

export class KpiCatalog1753813891639 implements MigrationInterface {
    name = 'KpiCatalog1753813891639'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "kpi_project" ALTER COLUMN "updated_at" datetime2`);
        await queryRunner.query(`ALTER TABLE "kpi_project" ALTER COLUMN "updated_by" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "kpi_deliverable_usage" ALTER COLUMN "updated_at" datetime2`);
        await queryRunner.query(`ALTER TABLE "kpi_deliverable_usage" ALTER COLUMN "updated_by" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "kpi_deliverable" ALTER COLUMN "updated_at" datetime2`);
        await queryRunner.query(`ALTER TABLE "kpi_deliverable" ALTER COLUMN "updated_by" uniqueidentifier`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "kpi_deliverable" ALTER COLUMN "updated_by" uniqueidentifier NOT NULL`);
        await queryRunner.query(`ALTER TABLE "kpi_deliverable" ALTER COLUMN "updated_at" datetime2 NOT NULL`);
        await queryRunner.query(`ALTER TABLE "kpi_deliverable_usage" ALTER COLUMN "updated_by" uniqueidentifier NOT NULL`);
        await queryRunner.query(`ALTER TABLE "kpi_deliverable_usage" ALTER COLUMN "updated_at" datetime2 NOT NULL`);
        await queryRunner.query(`ALTER TABLE "kpi_project" ALTER COLUMN "updated_by" uniqueidentifier NOT NULL`);
        await queryRunner.query(`ALTER TABLE "kpi_project" ALTER COLUMN "updated_at" datetime2 NOT NULL`);
    }

}
