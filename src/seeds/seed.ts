import { dataSource } from '../configs/datasource';
import { DeliverableTypeEntity } from '../entities/deliverable-type.entity';
import { DeliverableEntity } from '../entities/deliverable.entity';
import { ProjectEntity } from '../entities/project.entity';
import { v4 as uuidv4 } from 'uuid';

async function seed() {
  await dataSource.initialize();
  console.log('Seeding database...');

  const deliverableTypeRepo = dataSource.getRepository(DeliverableTypeEntity);
  const deliverableRepo = dataSource.getRepository(DeliverableEntity);
  const projectRepo = dataSource.getRepository(ProjectEntity);

  const now = new Date();

  // 1. Tipo de deliverable: YES_NO
  const type = deliverableTypeRepo.create({ code: 'YES_NO' });
  await deliverableTypeRepo.save(type);

  // 2. Project #1
  const project1 = projectRepo.create({
    uid: uuidv4(),
    name: 'Blueprint Program',
    function: 'PEOPLE',
    frequency: 'Monthly',
    is_active: true,
    source: 'Steerco',
    usage: 14,
    objective: 'Standardization initiative',
    date_start: new Date('2024-01-01'),
    date_end: new Date('2024-12-31'),
    origin: 'CATALOG',
    created_by: uuidv4(),
    updated_by: uuidv4(),
    created_at: now,
    updated_at: now,
    project_type: 'SIMPLE',
  });
  await projectRepo.save(project1);

  // 3. Deliverable #1
  const deliverable1 = deliverableRepo.create({
    uid: uuidv4(),
    name: 'People Ratio',
    function: 'PEOPLE',
    frequency: 'Monthly',
    is_active: true,
    source: 'PSI Dashboard',
    usage: 58,
    calculation_method: 'Improve vs LY',
    definition: 'Workforce structure and efficiency.',
    pa_value: '',
    bu_level_aggregation: 'Across BUs',
    is_cascading: false,
    created_by: uuidv4(),
    updated_by: uuidv4(),
    created_at: now,
    updated_at: now,
    deliverable_type: type,
    origin_project: project1,
    origin: 'CATALOG',
  });
  await deliverableRepo.save(deliverable1);

  // 4. Deliverable #2
  const deliverable2 = deliverableRepo.create({
    uid: uuidv4(),
    name: 'Blueprint Metric',
    function: 'PROCESS',
    frequency: 'Quarterly',
    is_active: true,
    source: 'Manual Entry',
    usage: 55,
    calculation_method: 'Sum of All',
    definition: 'Custom metric without project',
    pa_value: '',
    bu_level_aggregation: 'Global',
    is_cascading: false,
    created_by: uuidv4(),
    updated_by: uuidv4(),
    created_at: now,
    updated_at: now,
    deliverable_type: type,
    origin: 'CATALOG',
  });
  await deliverableRepo.save(deliverable2);

  // 5. Deliverable #3
  const deliverable3 = deliverableRepo.create({
    uid: uuidv4(),
    name: 'Efficiency Ratio',
    function: 'FINANCE',
    frequency: 'Monthly',
    is_active: true,
    source: 'Finance Dashboard',
    usage: 70,
    calculation_method: 'Net Revenue / Headcount',
    definition: 'Financial efficiency metric',
    pa_value: '',
    bu_level_aggregation: 'Region',
    is_cascading: true,
    created_by: uuidv4(),
    updated_by: uuidv4(),
    created_at: now,
    updated_at: now,
    deliverable_type: type,
    origin: 'CATALOG',
  });
  await deliverableRepo.save(deliverable3);

  // 6. Project #2 com deliverables associados via ManyToMany
  const project2 = projectRepo.create({
    uid: uuidv4(),
    name: 'Efficiency Initiative',
    function: 'FINANCE',
    frequency: 'Monthly',
    is_active: true,
    source: 'Leadership',
    usage: 22,
    objective: 'Boost efficiency across departments',
    date_start: new Date('2024-06-01'),
    date_end: new Date('2024-12-31'),
    origin: 'CATALOG',
    created_by: uuidv4(),
    updated_by: uuidv4(),
    created_at: now,
    updated_at: now,
    deliverable_links: [deliverable1, deliverable3],
    project_type: 'SIMPLE'
  });
  await projectRepo.save(project2);

  console.log('✅ Seed completed!');
  await dataSource.destroy();
}

seed().catch((err) => {
  console.error('❌ Seed failed:', err);
});
