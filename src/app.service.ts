import { Injectable, NotFoundException, NotImplementedException } from '@nestjs/common';
import { GetDeliverablesRequest } from './dtos/requests/get-deliverables.dto';
import { GetDeliverablesCompactResponse } from './dtos/responses/get-deliverables-compact.dto';
import { ProjectRepository } from './repositories/project.repository';
import { mapToView } from './mappers/deliverable.mapper';
import { DeliverableRepository } from './repositories/deliverable.repository';
import { GetProjectResponse } from './dtos/responses/get-project.dto';
import { CreateProjectRequest } from './dtos/requests/create-project.dto';
import { CreateDeliverableRequest } from './dtos/requests/create-deliverable.dto';
import { mapProjectToView } from './mappers/project.mapper';
import { UpdateProjectRequest } from './dtos/requests/update-project.dto';
import { UpdateDeliverableRequest } from './dtos/requests/update-deliverable.dto';
import { GetDeliverableResponse } from './dtos/responses/get-deliverable.dto';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class AppService {
  constructor(
    private readonly deliverableRepository: DeliverableRepository,
    private readonly projectRepository: ProjectRepository,
  ) { }

  getHealthCheck(): string {
    return 'OK';
  }

  async getDeliverableOrProject(uid: string, sessionUserUuid: string): Promise<GetDeliverableResponse> {
    console.log(sessionUserUuid);

    const project = await this.projectRepository.findByUid(uid);
    if (project) return mapToView(project);

    const deliverable = await this.deliverableRepository.findByUid(uid);
    if (deliverable) return mapToView(deliverable);

    throw new NotFoundException(`KPI or Project with uid ${uid} not found.`);
  }

  async getDeliverablesAndProjects(filters: GetDeliverablesRequest, sessionUserUuid: string): Promise<GetDeliverablesCompactResponse> {
    console.log(sessionUserUuid);

    const { search, year } = filters;
    const [projects, deliverables] = await Promise.all([
      this.projectRepository.findCompactWithFilters(search, year),
      this.deliverableRepository.findCompactStandaloneWithFilters(search),
    ]);

    return {
      data: [...projects.data, ...deliverables.data],
      pageNumber: 1,
      pageSize: projects.data.length + deliverables.data.length,
      totalRecords: projects.totalRecords + deliverables.totalRecords,
    };
  }

  async createDeliverable(kpiDto: CreateDeliverableRequest, sessionUserUuid: string): Promise<GetDeliverableResponse> {

    const entity = this.deliverableRepository.repository.create({
      ...kpiDto,
      created_at: new Date(),
      created_by: sessionUserUuid || uuidv4(),
      origin: 'CATALOG'
    });

    console.log(entity);

    const saved = await this.deliverableRepository.repository.save(entity);

    return mapToView(saved);
  }

  async createProject(projectDto: CreateProjectRequest, sessionUserUuid: string): Promise<GetProjectResponse> {
    let deliverablesEntities = [];

    console.log(projectDto);

    if (projectDto.deliverables && projectDto.deliverables.length > 0) {
      deliverablesEntities = await this.deliverableRepository.findByUids(projectDto.deliverables);

      console.log(deliverablesEntities);

      const foundUids = deliverablesEntities.map(d => d.uid);

      console.log(foundUids);

      const missingUids = projectDto.deliverables.filter(uid => !foundUids.includes(uid));

      if (missingUids.length > 0) {
        throw new NotFoundException(`Deliverable(s) with uid(s) ${missingUids.join(', ')} not found.`);
      }
    }

    const entity = this.projectRepository.repository.create({
      ...projectDto,
      deliverables: deliverablesEntities,
      created_at: new Date(),
      created_by: sessionUserUuid || uuidv4(),
      origin: 'CATALOG'
    });

    const saved = await this.projectRepository.repository.save(entity);

    return mapProjectToView(saved);
  }

  async updateDeliverable(uid: string, kpiDto: UpdateDeliverableRequest, sessionUserUuid: string): Promise<GetDeliverableResponse> {
    const deliverable = await this.deliverableRepository.findByUid(uid);
    if (!deliverable) {
      throw new NotFoundException(`KPI with uid ${uid} not found.`);
    }

    Object.assign(deliverable, {
      ...kpiDto,
      updated_at: new Date(),
      updated_by: sessionUserUuid || uuidv4(),
    });

    const saved = await this.deliverableRepository.repository.save(deliverable);
    return mapToView(saved);
  }

  async updateProject(uid: string, projectDto: UpdateProjectRequest, sessionUserUuid: string): Promise<GetProjectResponse> {
    const project = await this.projectRepository.findByUid(uid);
    if (!project) {
      throw new NotFoundException(`Project with uid ${uid} not found.`);
    }

    let deliverablesEntities = project.deliverables;

    if (projectDto.deliverables !== undefined) {
      if (projectDto.deliverables.length > 0) {
        deliverablesEntities = await this.deliverableRepository.findByUids(projectDto.deliverables);

        const foundUids = deliverablesEntities.map(d => d.uid);
        const missingUids = projectDto.deliverables.filter(uid => !foundUids.includes(uid));

        if (missingUids.length > 0) {
          throw new NotFoundException(`Deliverable(s) with uid(s) ${missingUids.join(', ')} not found.`);
        }
      } else {
        deliverablesEntities = [];
      }
    }

    Object.assign(project, {
      ...projectDto,
      deliverables: deliverablesEntities,
      updated_at: new Date(),
      updated_by: sessionUserUuid || uuidv4(),
    });

    const saved = await this.projectRepository.repository.save(project);
    return mapProjectToView(saved);
  }

  async softDeleteDeliverable(uid: string, sessionUserUuid: string): Promise<GetDeliverableResponse> {
    console.log(uid, sessionUserUuid);
    throw new NotImplementedException('Not implemented yet.');
  }

  async switchDeliverableStatus(
    uid: string,
    status: boolean,
    sessionUserUuid: string,
  ): Promise<GetDeliverableResponse> {
    console.log(uid, status, sessionUserUuid);
    throw new NotImplementedException('Not implemented yet.');
  }

}