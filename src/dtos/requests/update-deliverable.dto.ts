import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsBoolean, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { OwnerDto } from 'src/dtos/owner.dto';

export class UpdateDeliverableRequest {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  function?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  frequency?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  source?: string;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  is_active?: boolean;

  @ApiPropertyOptional({ type: [OwnerDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OwnerDto)
  @IsOptional()
  owners?: OwnerDto[];

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  bu_level_aggregation?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  calculation_method?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  definition?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  pa_value?: string;
}