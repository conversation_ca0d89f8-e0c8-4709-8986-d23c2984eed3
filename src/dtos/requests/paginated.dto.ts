import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNumber } from 'class-validator';
import { TransformNumber } from '../../helpers/tranformer.helper';

export class PaginatedRequest {
  @IsNumber()
  @ApiProperty()
  @Transform(TransformNumber)
  pageNumber: number;

  @IsNumber()
  @ApiProperty()
  @Transform(TransformNumber)
  pageSize: number;
}
