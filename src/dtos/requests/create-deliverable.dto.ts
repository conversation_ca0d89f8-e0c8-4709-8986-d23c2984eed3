import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsDate,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { OwnerDto } from 'src/dtos/owner.dto';
import { ProjectType, DeliverableType } from '../../enums';

export class CreateDeliverableRequest {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsString()
  function: string;

  @ApiProperty()
  @IsString()
  frequency: string;

  @ApiProperty()
  @IsString()
  source: string;

  @ApiProperty()
  @IsBoolean()
  is_active: boolean;

  @ApiProperty()
  @IsNumber()
  usage: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  calculation_method?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  definition?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  pa_value?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  bu_level_aggregation?: string;

  @ApiPropertyOptional({ enum: DeliverableType })
  @IsOptional()
  @IsEnum(DeliverableType)
  type?: DeliverableType;

  @ApiPropertyOptional({ type: [OwnerDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OwnerDto)
  @IsOptional()
  owners?: OwnerDto[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  objective?: string;

  @ApiPropertyOptional({ enum: ProjectType })
  @IsOptional()
  @IsEnum(ProjectType)
  project_type?: ProjectType;

  @ApiPropertyOptional({ type: [String], description: 'UIDs of deliverables' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  deliverables?: string[];

  @ApiPropertyOptional({ type: Date })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  date_start?: Date;

  @ApiPropertyOptional({ type: Date })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  date_end?: Date;
}
