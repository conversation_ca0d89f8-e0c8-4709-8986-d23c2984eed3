import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';
import { PaginatedRequest } from './paginated.dto';
import { Transform } from 'class-transformer';
import { TransformNumber } from 'src/helpers/tranformer.helper';

export class GetDeliverablesRequest extends PaginatedRequest {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty()
  @IsNumber()
  @Transform(TransformNumber)
  year: number;
}
