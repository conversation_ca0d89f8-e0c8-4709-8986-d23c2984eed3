import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsDate, IsEnum, IsArray } from 'class-validator';
import { Type } from 'class-transformer';
import { ProjectType } from '../../enums';

export class GetProjectResponse {
  @ApiProperty()
  @IsString()
  uid: string;

  @ApiProperty()
  @IsString()
  name: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  function?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  objective?: string;

  @ApiProperty({ enum: ProjectType })
  @IsEnum(ProjectType)
  project_type: ProjectType;

  @ApiPropertyOptional()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  date_start?: Date;

  @ApiPropertyOptional()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  date_end?: Date;

  @ApiPropertyOptional({ type: [String], description: 'UIDs of deliverables' })
  @IsOptional()
  @IsArray()
  deliverables?: string[];
}
