import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { Public } from './decorators/public.decorator';
import { User, UserSession } from './decorators/user.decorator';
import { CreateProjectRequest } from './dtos/requests/create-project.dto';
import { GetProjectResponse } from './dtos/responses/get-project.dto';
import { CreateDeliverableRequest } from './dtos/requests/create-deliverable.dto';
import { UpdateProjectRequest } from './dtos/requests/update-project.dto';
import { UpdateDeliverableRequest } from './dtos/requests/update-deliverable.dto';
import { GetDeliverableResponse } from './dtos/responses/get-deliverable.dto';
import { AppService } from './app.service';
import { GetDeliverablesRequest } from './dtos/requests/get-deliverables.dto';
import { GetDeliverablesCompactResponse } from './dtos/responses/get-deliverables-compact.dto';

@Controller()
export class AppController {
  constructor(
    private readonly appService: AppService,
  ) { }

  @Get('health-check')
  @Public()
  getHealthCheck(): string {
    return this.appService.getHealthCheck();
  }

  @Get('deliverables/:uid')
  async getDeliverableOrProject(
    @Param('uid') uid: string,
    @User() user: UserSession,
  ): Promise<GetDeliverableResponse> {
    return this.appService.getDeliverableOrProject(uid, user?.uuid);
  }

  @Get('deliverables')
  async getDeliverablesAndProjects(
    @Query() filters: GetDeliverablesRequest,
    @User() user: UserSession,
  ): Promise<GetDeliverablesCompactResponse> {
    return this.appService.getDeliverablesAndProjects(filters, user?.uuid);
  }

  @Post('deliverables')
  @HttpCode(HttpStatus.CREATED)
  async createDeliverable(
    @Body() kpiDto: CreateDeliverableRequest,
    @User() user: UserSession,
  ): Promise<GetDeliverableResponse> {
    return this.appService.createDeliverable(kpiDto, user?.uuid);
  }

  @Post('projects')
  @HttpCode(HttpStatus.CREATED)
  async createProject(
    @Body() projectDto: CreateProjectRequest,
    @User() user: UserSession,
  ): Promise<GetProjectResponse> {
    return this.appService.createProject(projectDto, user?.uuid);
  }

  @Put('deliverables/:uid')
  async updateDeliverable(
    @Param('uid') uid: string,
    @Body() kpiDto: UpdateDeliverableRequest,
    @User() user: UserSession,
  ): Promise<GetDeliverableResponse> {
    return this.appService.updateDeliverable(uid, kpiDto, user?.uuid);
  }

  @Put('projects/:uid')
  async updateProject(
    @Param('uid') uid: string,
    @Body() projectDto: UpdateProjectRequest,
    @User() user: UserSession,
  ): Promise<GetProjectResponse> {
    return this.appService.updateProject(uid, projectDto, user?.uuid);
  }

  @Patch('deliverables/:uid/:status')
  switchDeliverableStatus(
    @Param('uid') uid: string,
    @Param('status') status: boolean,
    @User() user: UserSession,
  ): Promise<GetDeliverableResponse> {
    return this.appService.switchDeliverableStatus(uid, status, user?.uuid);
  }

  @Delete('deliverables/:uid')
  softDeleteDeliverable(
    @Param('uid') uid: string,
    @User() user: UserSession,
  ): Promise<GetDeliverableResponse> {
    return this.appService.softDeleteDeliverable(uid, user?.uuid);
  }
}