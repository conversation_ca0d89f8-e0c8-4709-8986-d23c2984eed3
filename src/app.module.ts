import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { AppController } from './app.controller';
import { ConfigModule, ConfigService } from '@nestjs/config';
import typeorm from './configs/typeorm.config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ExtraLogMiddleware } from './middlewares/extra-log.middleware';
import { DataSourceOptions } from 'typeorm';
import { DeliverableEntity } from './entities/deliverable.entity';
import { ProjectEntity } from './entities/project.entity';
import { ProjectRepository } from './repositories/project.repository';
import { DeliverableRepository } from './repositories/deliverable.repository';
import { DeliverableTypeRepository } from './repositories/deliverable-type.repository';
import { DeliverableUsageRepository } from './repositories/deliverable-usage.repository';
import { DeliverableTypeEntity } from './entities/deliverable-type.entity';
import { DeliverableUsageEntity } from './entities/deliverable-usage.entity';
import { AppService } from './app.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [typeorm],
    }),
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: async (
        configService: ConfigService,
      ): Promise<DataSourceOptions> =>
        configService.get<DataSourceOptions>('typeorm'),
    }),
    TypeOrmModule.forFeature([
      DeliverableEntity,
      ProjectEntity,
      DeliverableTypeEntity,
      DeliverableUsageEntity
    ]),
  ],
  controllers: [AppController],
  providers: [
    AppService,
    ExtraLogMiddleware,
    ProjectRepository,
    DeliverableRepository,
    DeliverableTypeRepository,
    DeliverableUsageRepository
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(ExtraLogMiddleware).exclude({
      method: RequestMethod.GET,
      path: '*',
    });
  }
}
