# Swagger Test Examples - N+1 Query Fix Implementation

This document provides example payloads for testing the optimized `createProject` and `updateProject` endpoints that implement the N+1 query fix for deliverable validation.

## Overview

The N+1 query problem has been fixed in both endpoints by:
- Using `findByUids()` method to fetch multiple deliverables in a single query
- Implementing proper error handling for missing deliverables
- Maintaining the same API contract while improving performance

## POST /projects - Create Project

### Example 1: Create Project with Multiple Deliverables (N+1 Fix Test)

```json
{
  "name": "Q1 2024 Revenue Growth Initiative",
  "function": "Sales & Marketing",
  "objective": "Increase quarterly revenue by 15% through improved customer acquisition and retention strategies",
  "project_type": "MASTER",
  "date_start": "2024-01-01T00:00:00.000Z",
  "date_end": "2024-03-31T23:59:59.999Z",
  "deliverables": [
    "9A66A8C5-6088-4C09-A91D-5C2E09D49CEF",
    "2E3E9ED0-275F-491D-8B2F-B77543BF259A"
  ]
}
```

**Test Purpose**: Verify that multiple deliverables are fetched in a single query instead of N individual queries.

### Example 2: Create Project without Deliverables

```json
{
  "name": "Q2 2024 Market Research Project",
  "function": "Business Intelligence",
  "objective": "Conduct comprehensive market analysis for new product launch",
  "project_type": "SIMPLE",
  "date_start": "2024-04-01T00:00:00.000Z",
  "date_end": "2024-06-30T23:59:59.999Z"
}
```

**Test Purpose**: Verify that the endpoint works correctly when no deliverables are provided.

### Example 3: Create Project with Empty Deliverables Array

```json
{
  "name": "Q3 2024 Process Optimization",
  "function": "Operations",
  "objective": "Streamline internal processes to improve efficiency",
  "project_type": "SIMPLE",
  "date_start": "2024-07-01T00:00:00.000Z",
  "date_end": "2024-09-30T23:59:59.999Z",
  "deliverables": []
}
```

**Test Purpose**: Verify that empty deliverables array is handled correctly.

### Example 4: Create Project with Invalid Deliverable UIDs (Error Test)

```json
{
  "name": "Test Project for Error Handling",
  "function": "Quality Assurance",
  "objective": "Test error handling for invalid deliverable UIDs",
  "project_type": "SIMPLE",
  "date_start": "2024-01-01T00:00:00.000Z",
  "date_end": "2024-12-31T23:59:59.999Z",
  "deliverables": [
    "550e8400-e29b-41d4-a716-************",
    "invalid-uid-that-does-not-exist",
    "another-invalid-uid"
  ]
}
```

**Test Purpose**: Verify that proper error messages are returned when some deliverables are not found.
**Expected Response**: 404 Not Found with message listing the missing UIDs.

## PUT /projects/{uid} - Update Project

### Example 1: Update Project with New Deliverables (N+1 Fix Test)

```json
{
  "name": "Q1 2024 Revenue Growth Initiative - Updated",
  "function": "Sales & Marketing Operations",
  "objective": "Increase quarterly revenue by 20% through improved customer acquisition, retention strategies, and operational efficiency",
  "project_type": "SIMPLE",
  "date_start": "2024-01-15T00:00:00.000Z",
  "date_end": "2024-04-15T23:59:59.999Z",
  "deliverables": [
    "550e8400-e29b-41d4-a716-************",
    "550e8400-e29b-41d4-a716-************"
  ]
}
```

**Test Purpose**: Verify that deliverables are updated using optimized batch query.

### Example 2: Update Project - Clear All Deliverables

```json
{
  "name": "Updated Project Name",
  "deliverables": []
}
```

**Test Purpose**: Verify that passing an empty array clears all deliverables from the project.

### Example 3: Update Project - Keep Existing Deliverables

```json
{
  "name": "Updated Project Name Only",
  "function": "Updated Function",
  "objective": "Updated objective without changing deliverables"
}
```

**Test Purpose**: Verify that omitting the deliverables field keeps existing deliverables unchanged.

### Example 4: Update Project with Invalid Deliverable UIDs (Error Test)

```json
{
  "name": "Test Update Error Handling",
  "deliverables": [
    "550e8400-e29b-41d4-a716-************",
    "non-existent-deliverable-uid"
  ]
}
```

**Test Purpose**: Verify error handling during update when some deliverables are not found.
**Expected Response**: 404 Not Found with message listing the missing UIDs.

## Performance Testing

### Load Test Scenario

To verify the N+1 query fix effectiveness:

1. Create multiple projects with varying numbers of deliverables (1, 5, 10, 20 deliverables)
2. Monitor database query logs to confirm only one query per operation for deliverable fetching
3. Compare response times before and after the fix

### Database Query Monitoring

Before the fix, creating a project with 5 deliverables would generate:
- 1 query to create the project
- 5 individual queries to validate each deliverable (N+1 problem)

After the fix, the same operation generates:
- 1 query to create the project  
- 1 query to validate all deliverables (optimized)

## Error Scenarios

### Common Error Responses

**404 Not Found - Missing Deliverables:**
```json
{
  "statusCode": 404,
  "message": "Deliverable(s) with uid(s) invalid-uid-1, invalid-uid-2 not found.",
  "error": "Not Found"
}
```

**404 Not Found - Project Not Found (Update only):**
```json
{
  "statusCode": 404,
  "message": "Project with uid project-uid-that-does-not-exist not found.",
  "error": "Not Found"
}
```

## Testing Checklist

- [ ] Create project with multiple deliverables (verify single batch query)
- [ ] Create project without deliverables
- [ ] Create project with empty deliverables array
- [ ] Create project with invalid deliverable UIDs (error handling)
- [ ] Update project with new deliverables (verify single batch query)
- [ ] Update project to clear all deliverables
- [ ] Update project without changing deliverables
- [ ] Update project with invalid deliverable UIDs (error handling)
- [ ] Update non-existent project (error handling)
- [ ] Performance comparison before/after fix
